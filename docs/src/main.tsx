import ReactDOM from "react-dom/client";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, useTheme } from "@nui/ui";

type ThemeMode = "light" | "dark" | "auto";

// Example app component
function App() {
  const {
    mode,
    variant,
    isDark,
    availableVariants,
    setMode,
    setVariant,
    toggleMode,
    cycleVariant,
  } = useTheme();

  return (
    <div className="min-h-screen p-8 bg-background text-foreground">
      <div className="max-w-4xl mx-auto space-y-6">
        <h1 className="text-3xl font-bold">Theme System Demo</h1>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Current Theme Info */}
          <div className="p-4 bg-card border rounded-lg">
            <h2 className="text-xl font-semibold mb-3">Current Theme</h2>
            <div className="space-y-2 text-sm">
              <div>
                Mode: <code className="bg-muted px-2 py-1 rounded">{mode}</code>
              </div>
              <div>
                Variant:{" "}
                <code className="bg-muted px-2 py-1 rounded">{variant}</code>
              </div>
              <div>
                Is Dark:{" "}
                <code className="bg-muted px-2 py-1 rounded">
                  {isDark.toString()}
                </code>
              </div>
              <div>
                Available:{" "}
                <code className="bg-muted px-2 py-1 rounded">
                  {availableVariants.join(", ")}
                </code>
              </div>
            </div>
          </div>

          {/* Theme Controls */}
          <div className="p-4 bg-card border rounded-lg">
            <h2 className="text-xl font-semibold mb-3">Theme Controls</h2>
            <div className="space-y-3">
              <Button onClick={toggleMode} className="w-full">
                Toggle Mode ({mode})
              </Button>

              <Button
                onClick={cycleVariant}
                className="w-full"
                variant="secondary"
              >
                Cycle Variant ({variant})
              </Button>

              <div className="grid grid-cols-3 gap-2">
                {["light", "dark", "auto"].map((m) => (
                  <Button
                    key={m}
                    onClick={() => setMode(m as ThemeMode)}
                    variant={mode === m ? "default" : "outline"}
                    size="sm"
                  >
                    {m}
                  </Button>
                ))}
              </div>

              <div className="grid grid-cols-2 gap-2">
                {availableVariants.map((v) => (
                  <Button
                    key={v}
                    onClick={() => setVariant(v)}
                    variant={variant === v ? "default" : "outline"}
                    size="sm"
                  >
                    {v}
                  </Button>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Color Demonstration */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="p-4 bg-primary text-primary-foreground rounded-lg">
            <h3 className="font-semibold">Primary</h3>
            <p className="text-sm opacity-90">Primary colors</p>
          </div>
          <div className="p-4 bg-secondary text-secondary-foreground rounded-lg">
            <h3 className="font-semibold">Secondary</h3>
            <p className="text-sm opacity-90">Secondary colors</p>
          </div>
          <div className="p-4 bg-muted text-muted-foreground rounded-lg">
            <h3 className="font-semibold">Muted</h3>
            <p className="text-sm">Muted colors</p>
          </div>
          <div className="p-4 bg-accent text-accent-foreground rounded-lg">
            <h3 className="font-semibold">Accent</h3>
            <p className="text-sm opacity-90">Accent colors</p>
          </div>
        </div>

        {/* Destructive Example */}
        <div className="p-4 bg-destructive text-destructive-foreground rounded-lg">
          <h3 className="font-semibold">Destructive Action</h3>
          <p className="text-sm opacity-90">Used for dangerous actions</p>
        </div>
      </div>
    </div>
  );
}

ReactDOM.createRoot(document.getElementById("root")!).render(
  <ThemeProvider>
    <App />
  </ThemeProvider>,
);
