---
title: "Welcome to nui"
description: "A modern React component library built with TypeScript and Tailwind CSS"
---

# Welcome to nui

nui is a comprehensive React component library designed for modern web applications. Built with TypeScript and styled with Tailwind CSS, it provides a collection of accessible, customizable, and performant components.

## Features

- **TypeScript First**: Full TypeScript support with comprehensive type definitions
- **Accessible**: Built with accessibility in mind, following WAI-ARIA guidelines
- **Customizable**: Easily themeable with Tailwind CSS utilities
- **Modern**: Uses the latest React patterns and best practices
- **Tree Shakeable**: Import only what you need for optimal bundle size

## Quick Start

Get started with nui in your React project:

```bash
bun add @nui/ui
```

```tsx
import { Button } from "@nui/ui";

function App() {
  return <Button variant="primary">Hello nui!</Button>;
}
```

## Components

Explore our comprehensive collection of components:

- [Button](/components/button) - Interactive buttons with multiple variants
- [Card](/components/card) - Flexible content containers

## Getting Help

- Check out our [GitHub repository](https://github.com/your-org/nui)
- Join our community discussions
- Report issues and request features

---

Ready to build something amazing? Start exploring our components!
