---
title: "Input"
description: "Text input component for forms"
component: "Input"
---

# Input

The Input component is a fundamental form element for collecting user text input. It supports various types, states, and validation features.

## Import

```tsx
import { Input } from "@nui/ui";
```

## Usage

### Basic Input

```tsx
<Input placeholder="Enter your name" />
```

### Input with Label

```tsx
<div>
  <label htmlFor="email">Email</label>
  <Input id="email" type="email" placeholder="Enter your email" />
</div>
```

### Input Types

```tsx
<Input type="text" placeholder="Text input" />
<Input type="email" placeholder="Email input" />
<Input type="password" placeholder="Password input" />
<Input type="number" placeholder="Number input" />
```

## API Reference

### Input Props

| Prop          | Type                                          | Default  | Description                   |
| ------------- | --------------------------------------------- | -------- | ----------------------------- |
| `type`        | `"text" \| "email" \| "password" \| "number"` | `"text"` | The input type                |
| `placeholder` | `string`                                      | -        | Placeholder text              |
| `value`       | `string`                                      | -        | The input value               |
| `onChange`    | `(e: ChangeEvent<HTMLInputElement>) => void`  | -        | Change event handler          |
| `disabled`    | `boolean`                                     | `false`  | Whether the input is disabled |
| `className`   | `string`                                      | -        | Additional CSS classes        |

## Examples

### Controlled Input

```tsx
const [value, setValue] = useState("");

<Input
  value={value}
  onChange={(e) => setValue(e.target.value)}
  placeholder="Controlled input"
/>;
```

### Form Input

```tsx
<form>
  <div className="space-y-4">
    <div>
      <label htmlFor="firstName">First Name</label>
      <Input id="firstName" placeholder="John" />
    </div>
    <div>
      <label htmlFor="lastName">Last Name</label>
      <Input id="lastName" placeholder="Doe" />
    </div>
    <Button type="submit">Submit</Button>
  </div>
</form>
```
