{"name": "@nui/docs", "version": "0.1.0", "description": "nui documentation site", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "typecheck": "tsc --noEmit"}, "dependencies": {"@nui/ui": "*", "@tailwindcss/vite": "^4.1.11", "react": "^19.1.0", "react-dom": "^19.1.0", "tailwindcss": "^4.1.11"}, "devDependencies": {"@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "typescript": "^5.8.3", "vite": "^7.0.0"}}