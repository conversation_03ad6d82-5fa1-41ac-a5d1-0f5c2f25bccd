import { STORAGE_KEY, THEME_MODES } from "../lib/constants";
import type { ThemeConfig } from "../types/theme";

/** Safely read and parse a stored ThemeConfig from localStorage. Returns null if no valid stored theme exists. */
export function getStoredTheme(
  storageKey: string = STORAGE_KEY,
): Partial<ThemeConfig> | null {
  if (typeof window === "undefined") return null;

  try {
    const raw = localStorage.getItem(storageKey);
    if (!raw) return null;

    const parsed = JSON.parse(raw);
    const config: Partial<ThemeConfig> = {};

    if (THEME_MODES.includes(parsed.mode)) {
      config.mode = parsed.mode;
    }

    if (typeof parsed.variant === "string" && parsed.variant.trim()) {
      config.variant = parsed.variant;
    }

    return Object.keys(config).length > 0 ? config : null;
  } catch {
    return null;
  }
}

/** Persist a ThemeConfig to localStorage. Silently fails if storage is unavailable. */
export function persistTheme(
  config: ThemeConfig,
  storageKey: string = STORAGE_KEY,
): void {
  if (typeof window === "undefined") return;

  try {
    localStorage.setItem(storageKey, JSON.stringify(config));
  } catch {
    // Ignore write errors (e.g., quota exceeded, private mode)
  }
}
