import { useCallback, useState } from "react";

import { DEFAULT_CONFIG, THEME_MODES } from "../lib/constants";
import type { ThemeConfig, ThemeMode, ThemeVariant } from "../types/theme";
import { getStoredTheme } from "../utils/storage";

interface UseThemeConfigProps {
  defaultTheme?: Partial<ThemeConfig>;
  storageKey: string;
  disableStorage: boolean;
}

export function useThemeConfig({
  defaultTheme,
  storageKey,
  disableStorage,
}: UseThemeConfigProps) {
  const [config, setConfig] = useState<ThemeConfig>(() => ({
    ...DEFAULT_CONFIG,
    ...(disableStorage ? {} : getStoredTheme(storageKey)),
    ...defaultTheme,
  }));

  const setMode = useCallback(
    (mode: ThemeMode) => setConfig((prev) => ({ ...prev, mode })),
    [],
  );

  const setVariant = useCallback(
    (variant: ThemeVariant) => setConfig((prev) => ({ ...prev, variant })),
    [],
  );

  const toggleMode = useCallback(() => {
    setConfig((prev) => ({
      ...prev,
      mode: THEME_MODES[
        (THEME_MODES.indexOf(prev.mode) + 1) % THEME_MODES.length
      ],
    }));
  }, []);

  const cycleVariant = useCallback((availableVariants: ThemeVariant[]) => {
    setConfig((prev) => {
      const currentIndex = availableVariants.indexOf(prev.variant);
      const nextIndex = (currentIndex + 1) % availableVariants.length;
      return { ...prev, variant: availableVariants[nextIndex] };
    });
  }, []);

  return {
    config,
    setConfig,
    setMode,
    setVariant,
    toggleMode,
    cycleVariant,
  };
}
