import { useEffect } from "react";

import type { ThemeConfig } from "../types/theme";
import { persistTheme } from "../utils/storage";
import { applyTheme } from "../utils/theme";

interface UseThemeEffectsProps {
  config: ThemeConfig;
  storageKey: string;
  disableStorage: boolean;
}

export function useThemeEffects({
  config,
  storageKey,
  disableStorage,
}: UseThemeEffectsProps) {
  // Apply and persist theme changes
  useEffect(() => {
    applyTheme(config);
    if (!disableStorage) persistTheme(config, storageKey);
  }, [config, disableStorage, storageKey]);

  // Handle system theme changes in auto mode
  useEffect(() => {
    if (config.mode !== "auto") return;

    const mediaQuery = window.matchMedia("(prefers-color-scheme: dark)");
    const handleChange = () => applyTheme(config);

    mediaQuery.addEventListener("change", handleChange);
    return () => mediaQuery.removeEventListener("change", handleChange);
  }, [config]);
}
