export type ThemeMode = "light" | "dark" | "auto";
export type ThemeVariant = string;

export interface ThemeConfig {
  mode: ThemeMode;
  variant: ThemeVariant;
}

export interface ThemeContextValue {
  mode: ThemeMode;
  variant: ThemeVariant;
  isDark: boolean;
  availableVariants: string[];
  setMode: (mode: ThemeMode) => void;
  setVariant: (variant: ThemeVariant) => void;
  toggleMode: () => void;
  cycleVariant: () => void;
}
