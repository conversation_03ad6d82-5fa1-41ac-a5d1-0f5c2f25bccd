import React, { useMemo } from "react";

import { ThemeContext } from "../context/theme-context";
import { useThemeConfig } from "../hooks/use-theme-config";
import { useThemeEffects } from "../hooks/use-theme-effects";
import type { ThemeConfig, ThemeContextValue } from "../types/theme";
import { getAvailableVariants, isDarkMode } from "../utils/theme";

export interface ThemeProviderProps {
  children: React.ReactNode;
  defaultTheme?: Partial<ThemeConfig>;
  storageKey?: string;
  disableStorage?: boolean;
}

export function ThemeProvider({
  children,
  defaultTheme,
  storageKey = "theme",
  disableStorage = false,
}: ThemeProviderProps) {
  const { config, setMode, setVariant, toggleMode, cycleVariant } =
    useThemeConfig({
      defaultTheme,
      storageKey,
      disableStorage,
    });

  useThemeEffects({
    config,
    storageKey,
    disableStorage,
  });

  // Memoized computed values
  const availableVariants = useMemo(getAvailableVariants, []);
  const isDark = useMemo(() => isDarkMode(config), [config]);

  // Enhanced cycleVariant with available variants
  const handleCycleVariant = useMemo(
    () => () => cycleVariant(availableVariants),
    [cycleVariant, availableVariants],
  );

  const value = useMemo<ThemeContextValue>(
    () => ({
      mode: config.mode,
      variant: config.variant,
      isDark,
      availableVariants,
      setMode,
      setVariant,
      toggleMode,
      cycleVariant: handleCycleVariant,
    }),
    [
      config.mode,
      config.variant,
      isDark,
      availableVariants,
      setMode,
      setVariant,
      toggleMode,
      handleCycleVariant,
    ],
  );

  return (
    <ThemeContext.Provider value={value}>{children}</ThemeContext.Provider>
  );
}
