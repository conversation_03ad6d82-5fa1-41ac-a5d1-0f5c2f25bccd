{
  "$schema": "https://json.schemastore.org/tsconfig",
  "compilerOptions": {
    "target": "ES2022",
    "lib": ["ES2022", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "moduleResolution": "Bundler",
    "moduleDetection": "force",

    "strict": true,
    "noEmit": true,
    "skipLibCheck": true,
    "isolatedModules": true,
    "forceConsistentCasingInFileNames": true,

    "jsx": "react-jsx",
    "resolveJsonModule": true,
    "allowSyntheticDefaultImports": true,

    /* Build support - can be overridden by tools */
    "declaration": true,
    "declarationMap": true,
    "sourceMap": true,

    "types": ["vite/client", "node"]
  },
  "include": ["../**/*.ts", "../**/*.tsx", "tsup.config.ts"],
  "exclude": [
    "../node_modules",
    "../**/dist",
    "../**/*.test.*",
    "../**/*.spec.*"
  ]
}
