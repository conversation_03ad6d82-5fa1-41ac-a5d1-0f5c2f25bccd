{"name": "nui", "private": true, "workspaces": ["docs", "packages/*"], "scripts": {"dev": "bun --filter '*' dev", "build": "bun --filter '*' build", "lint": "oxlint --config .config/oxlintrc.json", "format": "prettier --config .config/prettierrc.json --write .", "check": "bun run lint && bun run format --check && bun run typecheck", "typecheck": "tsc --noEmit", "clean": "./scripts/clean.sh"}, "devDependencies": {"@ianvs/prettier-plugin-sort-imports": "^4.4.2", "@types/node": "^24.0.4", "clean-package": "^2.2.0", "oxlint": "^1.3.0", "prettier": "^3.6.1", "tsup": "^8.5.0", "typescript": "5.8.3"}, "engines": {"node": ">=20"}, "packageManager": "bun@1.2.17"}